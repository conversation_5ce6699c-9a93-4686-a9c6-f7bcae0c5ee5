{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Improved Introvert/Extrovert Classification Solution\n",
    "\n",
    "This solution implements the key recommendations from the analysis:\n",
    "1. **Advanced Feature Engineering** - Interactions, ratios, log transforms, polynomial features\n",
    "2. **Model Ensembling with Stacking** - Multi-level ensemble with meta-learner\n",
    "3. **Hyperparameter Tuning** - Optuna-based optimization\n",
    "4. **Pseudo-labeling** - Semi-supervised learning approach\n",
    "5. **Feature Selection** - Remove redundant and low-importance features\n",
    "6. **Advanced Classifiers** - Optimized XGBoost, LightGBM, CatBoost"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages\n",
    "!pip install optuna catboost lightgbm xgboost scikit-learn pandas numpy"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import numpy as np\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "from sklearn.model_selection import StratifiedKFold, cross_val_score\n",
    "from sklearn.preprocessing import LabelEncoder, StandardScaler\n",
    "from sklearn.ensemble import VotingClassifier, StackingClassifier\n",
    "from sklearn.linear_model import LogisticRegression\n",
    "from sklearn.metrics import accuracy_score, classification_report, roc_auc_score\n",
    "from sklearn.feature_selection import SelectKBest, f_classif, RFE\n",
    "\n",
    "from xgboost import XGBClassifier\n",
    "from catboost import CatBoostClassifier\n",
    "from lightgbm import LGBMClassifier\n",
    "\n",
    "import optuna\n",
    "from optuna.samplers import TPESampler\n",
    "\n",
    "print(\"Libraries imported successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Data Loading and Preprocessing"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load data\n",
    "print(\"Loading data...\")\n",
    "train_df = pd.read_csv(\"train.csv\")\n",
    "test_df = pd.read_csv(\"test.csv\")\n",
    "personality_df = pd.read_csv(\"personality_datasert.csv\")\n",
    "\n",
    "print(f\"Train shape: {train_df.shape}\")\n",
    "print(f\"Test shape: {test_df.shape}\")\n",
    "print(f\"Personality data shape: {personality_df.shape}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Data preprocessing and merging\n",
    "personality_df = (\n",
    "    personality_df\n",
    "    .rename(columns={'Personality': 'match_p'})\n",
    "    .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',\n",
    "                      'Going_outside', 'Drained_after_socializing', \n",
    "                      'Friends_circle_size', 'Post_frequency'])\n",
    ")\n",
    "\n",
    "merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',\n",
    "              'Going_outside', 'Drained_after_socializing', \n",
    "              'Friends_circle_size', 'Post_frequency']\n",
    "\n",
    "train_df = train_df.merge(personality_df, how='left', on=merge_cols)\n",
    "test_df = test_df.merge(personality_df, how='left', on=merge_cols)\n",
    "\n",
    "# Prepare target\n",
    "y_train = LabelEncoder().fit_transform(train_df['Personality'])\n",
    "test_ID = test_df['id'].values\n",
    "\n",
    "print(f\"Target distribution: {np.bincount(y_train)}\")\n",
    "print(f\"Class balance: {np.bincount(y_train) / len(y_train)}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Drop unnecessary columns\n",
    "drop_cols = ['id', 'Personality']\n",
    "if 'match_p' in train_df.columns:\n",
    "    # Check match_p missing rate\n",
    "    missing_rate = train_df['match_p'].isnull().sum() / len(train_df)\n",
    "    print(f\"match_p missing rate: {missing_rate:.3f}\")\n",
    "    if missing_rate > 0.8:  # Drop if more than 80% missing\n",
    "        drop_cols.append('match_p')\n",
    "        print(\"Dropping match_p due to high missing rate\")\n",
    "\n",
    "train_features = train_df.drop(columns=drop_cols)\n",
    "test_features = test_df.drop(columns=['id'] + ([col for col in drop_cols if col in test_df.columns and col != 'id']))\n",
    "\n",
    "# Combine for preprocessing\n",
    "all_data = pd.concat([train_features, test_features], ignore_index=True)\n",
    "ntrain = len(train_features)\n",
    "\n",
    "print(f\"Combined data shape: {all_data.shape}\")\n",
    "print(f\"Features: {list(all_data.columns)}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Handle missing values\n",
    "print(\"\\nMissing values before imputation:\")\n",
    "print(all_data.isnull().sum())\n",
    "\n",
    "numeric_features = all_data.select_dtypes(include=[np.number]).columns\n",
    "categorical_features = all_data.select_dtypes(include=['object']).columns\n",
    "\n",
    "# Fill missing values\n",
    "for col in numeric_features:\n",
    "    all_data[col].fillna(all_data[col].median(), inplace=True)\n",
    "\n",
    "for col in categorical_features:\n",
    "    all_data[col].fillna(all_data[col].mode()[0], inplace=True)\n",
    "\n",
    "# Encode categorical variables with ordinal encoding for better tree model performance\n",
    "label_encoders = {}\n",
    "for col in categorical_features:\n",
    "    le = LabelEncoder()\n",
    "    all_data[col] = le.fit_transform(all_data[col])\n",
    "    label_encoders[col] = le\n",
    "\n",
    "print(\"\\nMissing values after imputation:\")\n",
    "print(all_data.isnull().sum().sum())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Advanced Feature Engineering\n",
    "\n",
    "Creating domain-inspired features based on psychological insights about introversion/extroversion."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def create_advanced_features(df):\n",
    "    \"\"\"Create advanced features based on domain knowledge and recommendations\"\"\"\n",
    "    df_enhanced = df.copy()\n",
    "    \n",
    "    # 1. Interaction Features\n",
    "    # Friends circle size × Social event attendance (social engagement)\n",
    "    df_enhanced['social_engagement'] = df_enhanced['Friends_circle_size'] * df_enhanced['Social_event_attendance']\n",
    "    \n",
    "    # Time spent alone × Going outside (isolation vs activity balance)\n",
    "    df_enhanced['isolation_activity_balance'] = df_enhanced['Time_spent_Alone'] * df_enhanced['Going_outside']\n",
    "    \n",
    "    # Post frequency × Social event attendance (online vs offline social activity)\n",
    "    df_enhanced['online_offline_social'] = df_enhanced['Post_frequency'] * df_enhanced['Social_event_attendance']\n",
    "    \n",
    "    # 2. Ratio Features\n",
    "    # Post frequency / Social event attendance (digital vs real-world preference)\n",
    "    df_enhanced['digital_real_ratio'] = df_enhanced['Post_frequency'] / (df_enhanced['Social_event_attendance'] + 1e-8)\n",
    "    \n",
    "    # Time alone / Going outside (introversion tendency)\n",
    "    df_enhanced['introversion_tendency'] = df_enhanced['Time_spent_Alone'] / (df_enhanced['Going_outside'] + 1e-8)\n",
    "    \n",
    "    # Friends circle size / Social event attendance (social efficiency)\n",
    "    df_enhanced['social_efficiency'] = df_enhanced['Friends_circle_size'] / (df_enhanced['Social_event_attendance'] + 1e-8)\n",
    "    \n",
    "    # 3. Log Transformations for skewed features\n",
    "    # Log transform Time_spent_Alone (often skewed)\n",
    "    df_enhanced['log_time_alone'] = np.log1p(df_enhanced['Time_spent_Alone'])\n",
    "    \n",
    "    # Log transform Friends_circle_size\n",
    "    df_enhanced['log_friends_circle'] = np.log1p(df_enhanced['Friends_circle_size'])\n",
    "    \n",
    "    # 4. Binning features\n",
    "    # Bin Time_spent_Alone into categories\n",
    "    df_enhanced['time_alone_binned'] = pd.cut(df_enhanced['Time_spent_Alone'], \n",
    "                                            bins=[0, 2, 5, 8, float('inf')], \n",
    "                                            labels=[0, 1, 2, 3]).astype(int)\n",
    "    \n",
    "    # Bin Friends_circle_size\n",
    "    df_enhanced['friends_circle_binned'] = pd.cut(df_enhanced['Friends_circle_size'], \n",
    "                                                bins=[0, 5, 10, 15, float('inf')], \n",
    "                                                labels=[0, 1, 2, 3]).astype(int)\n",
    "    \n",
    "    # 5. Squared features (polynomial)\n",
    "    df_enhanced['time_alone_squared'] = df_enhanced['Time_spent_Alone'] ** 2\n",
    "    df_enhanced['social_events_squared'] = df_enhanced['Social_event_attendance'] ** 2\n",
    "    \n",
    "    # 6. Social activity score (composite feature)\n",
    "    df_enhanced['social_activity_score'] = (\n",
    "        df_enhanced['Social_event_attendance'] + \n",
    "        df_enhanced['Going_outside'] + \n",
    "        df_enhanced['Post_frequency']\n",
    "    ) / 3\n",
    "    \n",
    "    # 7. Introversion score (composite feature)\n",
    "    df_enhanced['introversion_score'] = (\n",
    "        df_enhanced['Time_spent_Alone'] - \n",
    "        df_enhanced['Social_event_attendance'] - \n",
    "        df_enhanced['Going_outside']\n",
    "    ) / 3\n",
    "    \n",
    "    return df_enhanced\n",
    "\n",
    "print(\"Creating advanced features...\")\n",
    "all_data_enhanced = create_advanced_features(all_data)\n",
    "\n",
    "# Split back to train and test\n",
    "X_train = all_data_enhanced[:ntrain]\n",
    "X_test = all_data_enhanced[ntrain:]\n",
    "\n",
    "print(f\"Enhanced feature set shape: {X_train.shape}\")\n",
    "print(f\"Original features: {len(all_data.columns)}\")\n",
    "print(f\"Enhanced features: {len(X_train.columns)}\")\n",
    "print(f\"New features created: {len(X_train.columns) - len(all_data.columns)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Feature Selection and Preprocessing"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature Selection\n",
    "print(\"Performing feature selection...\")\n",
    "\n",
    "# Remove highly correlated features\n",
    "correlation_matrix = X_train.corr().abs()\n",
    "upper_triangle = correlation_matrix.where(\n",
    "    np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)\n",
    ")\n",
    "\n",
    "# Find features with correlation > 0.95\n",
    "high_corr_features = [column for column in upper_triangle.columns if any(upper_triangle[column] > 0.95)]\n",
    "print(f\"Removing {len(high_corr_features)} highly correlated features: {high_corr_features}\")\n",
    "\n",
    "X_train_selected = X_train.drop(columns=high_corr_features)\n",
    "X_test_selected = X_test.drop(columns=high_corr_features)\n",
    "\n",
    "# Select top K features using statistical tests\n",
    "selector = SelectKBest(score_func=f_classif, k=min(25, X_train_selected.shape[1]))\n",
    "X_train_selected = pd.DataFrame(\n",
    "    selector.fit_transform(X_train_selected, y_train),\n",
    "    columns=X_train_selected.columns[selector.get_support()]\n",
    ")\n",
    "X_test_selected = pd.DataFrame(\n",
    "    selector.transform(X_test_selected),\n",
    "    columns=X_train_selected.columns\n",
    ")\n",
    "\n",
    "print(f\"Selected {X_train_selected.shape[1]} features after feature selection\")\n",
    "print(f\"Selected features: {list(X_train_selected.columns)}\")\n",
    "\n",
    "# Calculate class weights for imbalanced data\n",
    "class_0_count = np.sum(y_train == 0)\n",
    "class_1_count = np.sum(y_train == 1)\n",
    "scale_pos_weight = class_0_count / class_1_count\n",
    "print(f\"\\nClass distribution - 0: {class_0_count}, 1: {class_1_count}\")\n",
    "print(f\"Scale pos weight: {scale_pos_weight:.3f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Hyperparameter Tuning with Optuna"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def objective_xgb(trial):\n",
    "    \"\"\"Optuna objective function for XGBoost hyperparameter tuning\"\"\"\n",
    "    params = {\n",
    "        'max_depth': trial.suggest_int('max_depth', 3, 8),\n",
    "        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),\n",
    "        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),\n",
    "        'subsample': trial.suggest_float('subsample', 0.6, 1.0),\n",
    "        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),\n",
    "        'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),\n",
    "        'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),\n",
    "        'scale_pos_weight': scale_pos_weight,\n",
    "        'random_state': 42\n",
    "    }\n",
    "    \n",
    "    model = XGBClassifier(**params)\n",
    "    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')\n",
    "    return cv_scores.mean()\n",
    "\n",
    "def objective_lgbm(trial):\n",
    "    \"\"\"Optuna objective function for LightGBM hyperparameter tuning\"\"\"\n",
    "    params = {\n",
    "        'num_leaves': trial.suggest_int('num_leaves', 10, 100),\n",
    "        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),\n",
    "        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),\n",
    "        'subsample': trial.suggest_float('subsample', 0.6, 1.0),\n",
    "        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),\n",
    "        'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),\n",
    "        'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),\n",
    "        'class_weight': 'balanced',\n",
    "        'random_state': 42,\n",
    "        'verbose': -1\n",
    "    }\n",
    "    \n",
    "    model = LGBMClassifier(**params)\n",
    "    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')\n",
    "    return cv_scores.mean()\n",
    "\n",
    "def objective_catboost(trial):\n",
    "    \"\"\"Optuna objective function for CatBoost hyperparameter tuning\"\"\"\n",
    "    params = {\n",
    "        'depth': trial.suggest_int('depth', 4, 10),\n",
    "        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),\n",
    "        'iterations': trial.suggest_int('iterations', 100, 1000),\n",
    "        'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1, 10),\n",
    "        'class_weights': [scale_pos_weight, 1],\n",
    "        'random_seed': 42,\n",
    "        'verbose': False\n",
    "    }\n",
    "    \n",
    "    model = CatBoostClassifier(**params)\n",
    "    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')\n",
    "    return cv_scores.mean()\n",
    "\n",
    "print(\"Hyperparameter tuning functions defined.\")"
   ]
  }
