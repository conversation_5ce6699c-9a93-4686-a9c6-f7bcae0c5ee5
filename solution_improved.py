#!/usr/bin/env python3
"""
Improved Introvert/Extrovert Classification Solution

This solution implements the recommendations from the analysis:
1. Advanced Feature Engineering
2. Model Ensembling with Stacking
3. Hyperparameter Tuning with Optuna
4. Pseudo-labeling
5. Feature Selection
6. Advanced Classifiers (TabPFN if available)
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif, RFE

from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

import optuna
from optuna.samplers import TPESampler

# Load data
print("Loading data...")
train_df = pd.read_csv("train.csv")
test_df = pd.read_csv("test.csv")
personality_df = pd.read_csv("personality_datasert.csv")

# Data preprocessing and merging (same as original)
personality_df = (
    personality_df
    .rename(columns={'Personality': 'match_p'})
    .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency'])
)

merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
              'Going_outside', 'Drained_after_socializing', 
              'Friends_circle_size', 'Post_frequency']

train_df = train_df.merge(personality_df, how='left', on=merge_cols)
test_df = test_df.merge(personality_df, how='left', on=merge_cols)

# Prepare target
y_train = LabelEncoder().fit_transform(train_df['Personality'])
test_ID = test_df['id'].values

# Drop unnecessary columns
drop_cols = ['id', 'Personality']
if 'match_p' in train_df.columns:
    drop_cols.append('match_p')  # Drop match_p as it has too many missing values

train_features = train_df.drop(columns=drop_cols)
test_features = test_df.drop(columns=['id', 'match_p'] if 'match_p' in test_df.columns else ['id'])

# Combine for preprocessing
all_data = pd.concat([train_features, test_features], ignore_index=True)
ntrain = len(train_features)

# Handle missing values
numeric_features = all_data.select_dtypes(include=[np.number]).columns
categorical_features = all_data.select_dtypes(include=['object']).columns

# Fill missing values
for col in numeric_features:
    all_data[col].fillna(all_data[col].median(), inplace=True)

for col in categorical_features:
    all_data[col].fillna(all_data[col].mode()[0], inplace=True)

# Encode categorical variables with ordinal encoding for better tree model performance
label_encoders = {}
for col in categorical_features:
    le = LabelEncoder()
    all_data[col] = le.fit_transform(all_data[col])
    label_encoders[col] = le

def create_advanced_features(df):
    """Create advanced features based on domain knowledge and recommendations"""
    df_enhanced = df.copy()
    
    # 1. Interaction Features
    # Friends circle size × Social event attendance (social engagement)
    df_enhanced['social_engagement'] = df_enhanced['Friends_circle_size'] * df_enhanced['Social_event_attendance']
    
    # Time spent alone × Going outside (isolation vs activity balance)
    df_enhanced['isolation_activity_balance'] = df_enhanced['Time_spent_Alone'] * df_enhanced['Going_outside']
    
    # Post frequency × Social event attendance (online vs offline social activity)
    df_enhanced['online_offline_social'] = df_enhanced['Post_frequency'] * df_enhanced['Social_event_attendance']
    
    # 2. Ratio Features
    # Post frequency / Social event attendance (digital vs real-world preference)
    df_enhanced['digital_real_ratio'] = df_enhanced['Post_frequency'] / (df_enhanced['Social_event_attendance'] + 1e-8)
    
    # Time alone / Going outside (introversion tendency)
    df_enhanced['introversion_tendency'] = df_enhanced['Time_spent_Alone'] / (df_enhanced['Going_outside'] + 1e-8)
    
    # Friends circle size / Social event attendance (social efficiency)
    df_enhanced['social_efficiency'] = df_enhanced['Friends_circle_size'] / (df_enhanced['Social_event_attendance'] + 1e-8)
    
    # 3. Log Transformations for skewed features
    # Log transform Time_spent_Alone (often skewed)
    df_enhanced['log_time_alone'] = np.log1p(df_enhanced['Time_spent_Alone'])
    
    # Log transform Friends_circle_size
    df_enhanced['log_friends_circle'] = np.log1p(df_enhanced['Friends_circle_size'])
    
    # 4. Binning features
    # Bin Time_spent_Alone into categories
    df_enhanced['time_alone_binned'] = pd.cut(df_enhanced['Time_spent_Alone'], 
                                            bins=[0, 2, 5, 8, float('inf')], 
                                            labels=[0, 1, 2, 3]).astype(int)
    
    # Bin Friends_circle_size
    df_enhanced['friends_circle_binned'] = pd.cut(df_enhanced['Friends_circle_size'], 
                                                bins=[0, 5, 10, 15, float('inf')], 
                                                labels=[0, 1, 2, 3]).astype(int)
    
    # 5. Squared features (polynomial)
    df_enhanced['time_alone_squared'] = df_enhanced['Time_spent_Alone'] ** 2
    df_enhanced['social_events_squared'] = df_enhanced['Social_event_attendance'] ** 2
    
    # 6. Social activity score (composite feature)
    df_enhanced['social_activity_score'] = (
        df_enhanced['Social_event_attendance'] + 
        df_enhanced['Going_outside'] + 
        df_enhanced['Post_frequency']
    ) / 3
    
    # 7. Introversion score (composite feature)
    df_enhanced['introversion_score'] = (
        df_enhanced['Time_spent_Alone'] - 
        df_enhanced['Social_event_attendance'] - 
        df_enhanced['Going_outside']
    ) / 3
    
    return df_enhanced

print("Creating advanced features...")
all_data_enhanced = create_advanced_features(all_data)

# Split back to train and test
X_train = all_data_enhanced[:ntrain]
X_test = all_data_enhanced[ntrain:]

print(f"Enhanced feature set shape: {X_train.shape}")
print(f"Original features: {len(all_data.columns)}")
print(f"Enhanced features: {len(X_train.columns)}")
