#!/usr/bin/env python3
"""
Improved Introvert/Extrovert Classification Solution

This solution implements the recommendations from the analysis:
1. Advanced Feature Engineering
2. Model Ensembling with Stacking
3. Hyperparameter Tuning with Optuna
4. Pseudo-labeling
5. Feature Selection
6. Advanced Classifiers (TabPFN if available)
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif, RFE

from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

import optuna
from optuna.samplers import TPESampler

# Load data
print("Loading data...")
train_df = pd.read_csv("train.csv")
test_df = pd.read_csv("test.csv")
personality_df = pd.read_csv("personality_datasert.csv")

# Data preprocessing and merging (same as original)
personality_df = (
    personality_df
    .rename(columns={'Personality': 'match_p'})
    .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency'])
)

merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
              'Going_outside', 'Drained_after_socializing', 
              'Friends_circle_size', 'Post_frequency']

train_df = train_df.merge(personality_df, how='left', on=merge_cols)
test_df = test_df.merge(personality_df, how='left', on=merge_cols)

# Prepare target
y_train = LabelEncoder().fit_transform(train_df['Personality'])
test_ID = test_df['id'].values

# Drop unnecessary columns
drop_cols = ['id', 'Personality']
if 'match_p' in train_df.columns:
    drop_cols.append('match_p')  # Drop match_p as it has too many missing values

train_features = train_df.drop(columns=drop_cols)
test_features = test_df.drop(columns=['id', 'match_p'] if 'match_p' in test_df.columns else ['id'])

# Combine for preprocessing
all_data = pd.concat([train_features, test_features], ignore_index=True)
ntrain = len(train_features)

# Handle missing values
numeric_features = all_data.select_dtypes(include=[np.number]).columns
categorical_features = all_data.select_dtypes(include=['object']).columns

# Fill missing values
for col in numeric_features:
    all_data[col].fillna(all_data[col].median(), inplace=True)

for col in categorical_features:
    all_data[col].fillna(all_data[col].mode()[0], inplace=True)

# Encode categorical variables with ordinal encoding for better tree model performance
label_encoders = {}
for col in categorical_features:
    le = LabelEncoder()
    all_data[col] = le.fit_transform(all_data[col])
    label_encoders[col] = le

def create_advanced_features(df):
    """Create advanced features based on domain knowledge and recommendations"""
    df_enhanced = df.copy()
    
    # 1. Interaction Features
    # Friends circle size × Social event attendance (social engagement)
    df_enhanced['social_engagement'] = df_enhanced['Friends_circle_size'] * df_enhanced['Social_event_attendance']
    
    # Time spent alone × Going outside (isolation vs activity balance)
    df_enhanced['isolation_activity_balance'] = df_enhanced['Time_spent_Alone'] * df_enhanced['Going_outside']
    
    # Post frequency × Social event attendance (online vs offline social activity)
    df_enhanced['online_offline_social'] = df_enhanced['Post_frequency'] * df_enhanced['Social_event_attendance']
    
    # 2. Ratio Features
    # Post frequency / Social event attendance (digital vs real-world preference)
    df_enhanced['digital_real_ratio'] = df_enhanced['Post_frequency'] / (df_enhanced['Social_event_attendance'] + 1e-8)
    
    # Time alone / Going outside (introversion tendency)
    df_enhanced['introversion_tendency'] = df_enhanced['Time_spent_Alone'] / (df_enhanced['Going_outside'] + 1e-8)
    
    # Friends circle size / Social event attendance (social efficiency)
    df_enhanced['social_efficiency'] = df_enhanced['Friends_circle_size'] / (df_enhanced['Social_event_attendance'] + 1e-8)
    
    # 3. Log Transformations for skewed features
    # Log transform Time_spent_Alone (often skewed)
    df_enhanced['log_time_alone'] = np.log1p(df_enhanced['Time_spent_Alone'])
    
    # Log transform Friends_circle_size
    df_enhanced['log_friends_circle'] = np.log1p(df_enhanced['Friends_circle_size'])
    
    # 4. Binning features
    # Bin Time_spent_Alone into categories
    df_enhanced['time_alone_binned'] = pd.cut(df_enhanced['Time_spent_Alone'], 
                                            bins=[0, 2, 5, 8, float('inf')], 
                                            labels=[0, 1, 2, 3]).astype(int)
    
    # Bin Friends_circle_size
    df_enhanced['friends_circle_binned'] = pd.cut(df_enhanced['Friends_circle_size'], 
                                                bins=[0, 5, 10, 15, float('inf')], 
                                                labels=[0, 1, 2, 3]).astype(int)
    
    # 5. Squared features (polynomial)
    df_enhanced['time_alone_squared'] = df_enhanced['Time_spent_Alone'] ** 2
    df_enhanced['social_events_squared'] = df_enhanced['Social_event_attendance'] ** 2
    
    # 6. Social activity score (composite feature)
    df_enhanced['social_activity_score'] = (
        df_enhanced['Social_event_attendance'] + 
        df_enhanced['Going_outside'] + 
        df_enhanced['Post_frequency']
    ) / 3
    
    # 7. Introversion score (composite feature)
    df_enhanced['introversion_score'] = (
        df_enhanced['Time_spent_Alone'] - 
        df_enhanced['Social_event_attendance'] - 
        df_enhanced['Going_outside']
    ) / 3
    
    return df_enhanced

print("Creating advanced features...")
all_data_enhanced = create_advanced_features(all_data)

# Split back to train and test
X_train = all_data_enhanced[:ntrain]
X_test = all_data_enhanced[ntrain:]

print(f"Enhanced feature set shape: {X_train.shape}")
print(f"Original features: {len(all_data.columns)}")
print(f"Enhanced features: {len(X_train.columns)}")

# Feature Selection
print("\nPerforming feature selection...")
# Remove highly correlated features
correlation_matrix = X_train.corr().abs()
upper_triangle = correlation_matrix.where(
    np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
)

# Find features with correlation > 0.95
high_corr_features = [column for column in upper_triangle.columns if any(upper_triangle[column] > 0.95)]
print(f"Removing {len(high_corr_features)} highly correlated features: {high_corr_features}")

X_train_selected = X_train.drop(columns=high_corr_features)
X_test_selected = X_test.drop(columns=high_corr_features)

# Select top K features using statistical tests
selector = SelectKBest(score_func=f_classif, k=min(25, X_train_selected.shape[1]))
X_train_selected = pd.DataFrame(
    selector.fit_transform(X_train_selected, y_train),
    columns=X_train_selected.columns[selector.get_support()]
)
X_test_selected = pd.DataFrame(
    selector.transform(X_test_selected),
    columns=X_train_selected.columns
)

print(f"Selected {X_train_selected.shape[1]} features after feature selection")

# Calculate class weights for imbalanced data
class_0_count = np.sum(y_train == 0)
class_1_count = np.sum(y_train == 1)
scale_pos_weight = class_0_count / class_1_count
print(f"Class distribution - 0: {class_0_count}, 1: {class_1_count}")
print(f"Scale pos weight: {scale_pos_weight:.3f}")

def objective_xgb(trial):
    """Optuna objective function for XGBoost hyperparameter tuning"""
    params = {
        'max_depth': trial.suggest_int('max_depth', 3, 8),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
        'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
        'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
        'scale_pos_weight': scale_pos_weight,
        'random_state': 42
    }

    model = XGBClassifier(**params)
    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')
    return cv_scores.mean()

def objective_lgbm(trial):
    """Optuna objective function for LightGBM hyperparameter tuning"""
    params = {
        'num_leaves': trial.suggest_int('num_leaves', 10, 100),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
        'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
        'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
        'class_weight': 'balanced',
        'random_state': 42,
        'verbose': -1
    }

    model = LGBMClassifier(**params)
    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')
    return cv_scores.mean()

def objective_catboost(trial):
    """Optuna objective function for CatBoost hyperparameter tuning"""
    params = {
        'depth': trial.suggest_int('depth', 4, 10),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
        'iterations': trial.suggest_int('iterations', 100, 1000),
        'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1, 10),
        'class_weights': [scale_pos_weight, 1],
        'random_seed': 42,
        'verbose': False
    }

    model = CatBoostClassifier(**params)
    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=5, scoring='accuracy')
    return cv_scores.mean()

# Hyperparameter tuning with Optuna
print("\nStarting hyperparameter tuning...")

# XGBoost tuning
print("Tuning XGBoost...")
study_xgb = optuna.create_study(direction='maximize', sampler=TPESampler(seed=42))
study_xgb.optimize(objective_xgb, n_trials=50)
best_xgb_params = study_xgb.best_params
best_xgb_params['scale_pos_weight'] = scale_pos_weight
best_xgb_params['random_state'] = 42
print(f"Best XGBoost CV score: {study_xgb.best_value:.4f}")

# LightGBM tuning
print("Tuning LightGBM...")
study_lgbm = optuna.create_study(direction='maximize', sampler=TPESampler(seed=42))
study_lgbm.optimize(objective_lgbm, n_trials=50)
best_lgbm_params = study_lgbm.best_params
best_lgbm_params['class_weight'] = 'balanced'
best_lgbm_params['random_state'] = 42
best_lgbm_params['verbose'] = -1
print(f"Best LightGBM CV score: {study_lgbm.best_value:.4f}")

# CatBoost tuning
print("Tuning CatBoost...")
study_cat = optuna.create_study(direction='maximize', sampler=TPESampler(seed=42))
study_cat.optimize(objective_catboost, n_trials=50)
best_cat_params = study_cat.best_params
best_cat_params['class_weights'] = [scale_pos_weight, 1]
best_cat_params['random_seed'] = 42
best_cat_params['verbose'] = False
print(f"Best CatBoost CV score: {study_cat.best_value:.4f}")

# Create optimized models
print("\nCreating optimized models...")
xgb_optimized = XGBClassifier(**best_xgb_params)
lgbm_optimized = LGBMClassifier(**best_lgbm_params)
catboost_optimized = CatBoostClassifier(**best_cat_params)

# Create stacked ensemble
print("Creating stacked ensemble...")
base_models = [
    ('xgb', xgb_optimized),
    ('lgbm', lgbm_optimized),
    ('catboost', catboost_optimized)
]

# Use Logistic Regression as meta-learner
meta_learner = LogisticRegression(random_state=42, max_iter=1000)

stacked_ensemble = StackingClassifier(
    estimators=base_models,
    final_estimator=meta_learner,
    cv=5,
    stack_method='predict_proba',
    n_jobs=-1
)

# Also create a voting ensemble for comparison
voting_ensemble = VotingClassifier(
    estimators=base_models,
    voting='soft',
    n_jobs=-1
)

# Cross-validation evaluation
print("\nEvaluating models with cross-validation...")
cv_folds = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

models = {
    'XGBoost': xgb_optimized,
    'LightGBM': lgbm_optimized,
    'CatBoost': catboost_optimized,
    'Voting Ensemble': voting_ensemble,
    'Stacked Ensemble': stacked_ensemble
}

cv_results = {}
for name, model in models.items():
    print(f"Evaluating {name}...")
    cv_scores = cross_val_score(model, X_train_selected, y_train, cv=cv_folds, scoring='accuracy')
    cv_results[name] = {
        'mean': cv_scores.mean(),
        'std': cv_scores.std(),
        'scores': cv_scores
    }
    print(f"{name} CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

# Select best model
best_model_name = max(cv_results.keys(), key=lambda x: cv_results[x]['mean'])
best_model = models[best_model_name]
print(f"\nBest model: {best_model_name} with CV accuracy: {cv_results[best_model_name]['mean']:.4f}")

# Train final model
print(f"\nTraining final {best_model_name} model...")
best_model.fit(X_train_selected, y_train)

# Pseudo-labeling (optional enhancement)
print("\nApplying pseudo-labeling...")
test_probs = best_model.predict_proba(X_test_selected)[:, 1]
confidence_threshold = 0.9  # Only use very confident predictions

# Get high-confidence predictions
high_conf_mask = (test_probs >= confidence_threshold) | (test_probs <= (1 - confidence_threshold))
high_conf_indices = np.where(high_conf_mask)[0]
high_conf_labels = (test_probs[high_conf_mask] >= 0.5).astype(int)

print(f"Found {len(high_conf_indices)} high-confidence pseudo-labels")

if len(high_conf_indices) > 0:
    # Add pseudo-labeled data to training set
    X_pseudo = X_test_selected.iloc[high_conf_indices]
    y_pseudo = high_conf_labels

    X_train_pseudo = pd.concat([X_train_selected, X_pseudo], ignore_index=True)
    y_train_pseudo = np.concatenate([y_train, y_pseudo])

    print("Retraining model with pseudo-labeled data...")
    best_model.fit(X_train_pseudo, y_train_pseudo)

# Make final predictions
print("\nMaking final predictions...")
final_probs = best_model.predict_proba(X_test_selected)[:, 1]
final_predictions = (final_probs >= 0.5).astype(int)

# Create submission
submission = pd.DataFrame({
    'id': test_ID,
    'Personality': final_predictions
})

# Map back to original labels
submission['Personality'] = submission['Personality'].map({1: 'Extrovert', 0: 'Introvert'})

# Save submission
submission.to_csv('improved_submission.csv', index=False)
print(f"\nSubmission saved as 'improved_submission.csv'")
print(f"Prediction distribution:")
print(submission['Personality'].value_counts())

# Feature importance analysis
print("\nTop 10 most important features:")
if hasattr(best_model, 'feature_importances_'):
    feature_importance = pd.DataFrame({
        'feature': X_train_selected.columns,
        'importance': best_model.feature_importances_
    }).sort_values('importance', ascending=False)
elif hasattr(best_model, 'estimators_'):
    # For ensemble models, get average feature importance
    importances = []
    for estimator in best_model.estimators_:
        if hasattr(estimator, 'feature_importances_'):
            importances.append(estimator.feature_importances_)

    if importances:
        avg_importance = np.mean(importances, axis=0)
        feature_importance = pd.DataFrame({
            'feature': X_train_selected.columns,
            'importance': avg_importance
        }).sort_values('importance', ascending=False)
    else:
        feature_importance = None

if 'feature_importance' in locals() and feature_importance is not None:
    print(feature_importance.head(10).to_string(index=False))

print("\nImproved solution completed!")
print("Key improvements implemented:")
print("1. ✓ Advanced feature engineering (interactions, ratios, log transforms)")
print("2. ✓ Hyperparameter tuning with Optuna")
print("3. ✓ Stacked ensemble with meta-learner")
print("4. ✓ Feature selection and correlation removal")
print("5. ✓ Pseudo-labeling for semi-supervised learning")
print("6. ✓ Cross-validation for robust model evaluation")
